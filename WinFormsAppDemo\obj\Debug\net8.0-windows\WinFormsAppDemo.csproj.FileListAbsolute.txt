F:\SolutionWinFormDemo\WinFormsAppDemo\bin\Debug\net8.0-windows\WinFormsAppDemo.exe
F:\SolutionWinFormDemo\WinFormsAppDemo\bin\Debug\net8.0-windows\WinFormsAppDemo.deps.json
F:\SolutionWinFormDemo\WinFormsAppDemo\bin\Debug\net8.0-windows\WinFormsAppDemo.runtimeconfig.json
F:\SolutionWinFormDemo\WinFormsAppDemo\bin\Debug\net8.0-windows\WinFormsAppDemo.dll
F:\SolutionWinFormDemo\WinFormsAppDemo\bin\Debug\net8.0-windows\WinFormsAppDemo.pdb
F:\SolutionWinFormDemo\WinFormsAppDemo\bin\Debug\net8.0-windows\FreeSql.dll
F:\SolutionWinFormDemo\WinFormsAppDemo\bin\Debug\net8.0-windows\FreeSql.Provider.Sqlite.dll
F:\SolutionWinFormDemo\WinFormsAppDemo\bin\Debug\net8.0-windows\Microsoft.Extensions.Configuration.dll
F:\SolutionWinFormDemo\WinFormsAppDemo\bin\Debug\net8.0-windows\Microsoft.Extensions.Configuration.Abstractions.dll
F:\SolutionWinFormDemo\WinFormsAppDemo\bin\Debug\net8.0-windows\Microsoft.Extensions.Configuration.Binder.dll
F:\SolutionWinFormDemo\WinFormsAppDemo\bin\Debug\net8.0-windows\Microsoft.Extensions.Configuration.FileExtensions.dll
F:\SolutionWinFormDemo\WinFormsAppDemo\bin\Debug\net8.0-windows\Microsoft.Extensions.Configuration.Json.dll
F:\SolutionWinFormDemo\WinFormsAppDemo\bin\Debug\net8.0-windows\Microsoft.Extensions.DependencyInjection.dll
F:\SolutionWinFormDemo\WinFormsAppDemo\bin\Debug\net8.0-windows\Microsoft.Extensions.DependencyInjection.Abstractions.dll
F:\SolutionWinFormDemo\WinFormsAppDemo\bin\Debug\net8.0-windows\Microsoft.Extensions.DependencyModel.dll
F:\SolutionWinFormDemo\WinFormsAppDemo\bin\Debug\net8.0-windows\Microsoft.Extensions.FileProviders.Abstractions.dll
F:\SolutionWinFormDemo\WinFormsAppDemo\bin\Debug\net8.0-windows\Microsoft.Extensions.FileProviders.Physical.dll
F:\SolutionWinFormDemo\WinFormsAppDemo\bin\Debug\net8.0-windows\Microsoft.Extensions.FileSystemGlobbing.dll
F:\SolutionWinFormDemo\WinFormsAppDemo\bin\Debug\net8.0-windows\Microsoft.Extensions.Logging.dll
F:\SolutionWinFormDemo\WinFormsAppDemo\bin\Debug\net8.0-windows\Microsoft.Extensions.Logging.Abstractions.dll
F:\SolutionWinFormDemo\WinFormsAppDemo\bin\Debug\net8.0-windows\Microsoft.Extensions.Options.dll
F:\SolutionWinFormDemo\WinFormsAppDemo\bin\Debug\net8.0-windows\Microsoft.Extensions.Primitives.dll
F:\SolutionWinFormDemo\WinFormsAppDemo\bin\Debug\net8.0-windows\Serilog.dll
F:\SolutionWinFormDemo\WinFormsAppDemo\bin\Debug\net8.0-windows\Serilog.Extensions.Logging.dll
F:\SolutionWinFormDemo\WinFormsAppDemo\bin\Debug\net8.0-windows\Serilog.Settings.Configuration.dll
F:\SolutionWinFormDemo\WinFormsAppDemo\bin\Debug\net8.0-windows\Serilog.Sinks.Console.dll
F:\SolutionWinFormDemo\WinFormsAppDemo\bin\Debug\net8.0-windows\Serilog.Sinks.File.dll
F:\SolutionWinFormDemo\WinFormsAppDemo\bin\Debug\net8.0-windows\System.Data.SQLite.dll
F:\SolutionWinFormDemo\WinFormsAppDemo\bin\Debug\net8.0-windows\System.Diagnostics.DiagnosticSource.dll
F:\SolutionWinFormDemo\WinFormsAppDemo\bin\Debug\net8.0-windows\System.IO.Pipelines.dll
F:\SolutionWinFormDemo\WinFormsAppDemo\bin\Debug\net8.0-windows\System.Text.Encodings.Web.dll
F:\SolutionWinFormDemo\WinFormsAppDemo\bin\Debug\net8.0-windows\System.Text.Json.dll
F:\SolutionWinFormDemo\WinFormsAppDemo\bin\Debug\net8.0-windows\runtimes\linux-x64\native\SQLite.Interop.dll
F:\SolutionWinFormDemo\WinFormsAppDemo\bin\Debug\net8.0-windows\runtimes\osx-x64\native\SQLite.Interop.dll
F:\SolutionWinFormDemo\WinFormsAppDemo\bin\Debug\net8.0-windows\runtimes\win-x64\native\SQLite.Interop.dll
F:\SolutionWinFormDemo\WinFormsAppDemo\bin\Debug\net8.0-windows\runtimes\win-x86\native\SQLite.Interop.dll
F:\SolutionWinFormDemo\WinFormsAppDemo\bin\Debug\net8.0-windows\runtimes\browser\lib\net8.0\System.Text.Encodings.Web.dll
F:\SolutionWinFormDemo\WinFormsAppDemo\obj\Debug\net8.0-windows\WinFormsAppDemo.csproj.AssemblyReference.cache
F:\SolutionWinFormDemo\WinFormsAppDemo\obj\Debug\net8.0-windows\WinFormsAppDemo.Forms.FormMain.resources
F:\SolutionWinFormDemo\WinFormsAppDemo\obj\Debug\net8.0-windows\WinFormsAppDemo.csproj.GenerateResource.cache
F:\SolutionWinFormDemo\WinFormsAppDemo\obj\Debug\net8.0-windows\WinFormsAppDemo.GeneratedMSBuildEditorConfig.editorconfig
F:\SolutionWinFormDemo\WinFormsAppDemo\obj\Debug\net8.0-windows\WinFormsAppDemo.AssemblyInfoInputs.cache
F:\SolutionWinFormDemo\WinFormsAppDemo\obj\Debug\net8.0-windows\WinFormsAppDemo.AssemblyInfo.cs
F:\SolutionWinFormDemo\WinFormsAppDemo\obj\Debug\net8.0-windows\WinFormsAppDemo.csproj.CoreCompileInputs.cache
F:\SolutionWinFormDemo\WinFormsAppDemo\obj\Debug\net8.0-windows\WinForms.75B8845F.Up2Date
F:\SolutionWinFormDemo\WinFormsAppDemo\obj\Debug\net8.0-windows\WinFormsAppDemo.dll
F:\SolutionWinFormDemo\WinFormsAppDemo\obj\Debug\net8.0-windows\refint\WinFormsAppDemo.dll
F:\SolutionWinFormDemo\WinFormsAppDemo\obj\Debug\net8.0-windows\WinFormsAppDemo.pdb
F:\SolutionWinFormDemo\WinFormsAppDemo\obj\Debug\net8.0-windows\WinFormsAppDemo.genruntimeconfig.cache
F:\SolutionWinFormDemo\WinFormsAppDemo\obj\Debug\net8.0-windows\ref\WinFormsAppDemo.dll
