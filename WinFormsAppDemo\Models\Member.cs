﻿namespace WinFormsAppDemo.Models;

public class Member
{
    public int Id { get; set; }
    public string Account { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string NickName { get; set; } = string.Empty;
    public string RemarkName { get; set; } = string.Empty;
    public decimal Balance { get; set; }
    public decimal RebatePercent { get; set; }
    public string ParentAccount { get; set; } = string.Empty;
    public DateTime CreateTime { get; set; } = DateTime.Now;
    public bool IsActive { get; set; }
    public bool Deleted { get; set; }
}