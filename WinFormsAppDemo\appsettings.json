﻿{
  "ConnectionStrings": {
    "DefaultConnection": "Data Source=Demo.db;Version=3;Pooling=true;Max Pool Size=100;Min Pool Size=5;"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "Serilog": {
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Warning",
        "System": "Warning"
      }
    },
    "WriteTo": [
      {
        "Name": "Console",
        "Args": {
          "outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}"
        }
      },
      {
        "Name": "File",
        "Args": {
          "path": "logs/.log",
          "rollingInterval": "Day",
          "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj}{NewLine}{Exception}",
          "retainedFileCountLimit": 3
        }
      }
    ]
  },
  "Application": {
    "Name": "WinForms Dependency Injection Demo",
    "Version": "1.0.0",
    "EnableAutoSyncStructure": true
  }
}
