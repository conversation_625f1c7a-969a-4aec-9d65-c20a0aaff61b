using Microsoft.Extensions.Logging;
using WinFormsAppDemo.Interfaces;

namespace WinFormsAppDemo.Forms;

public partial class FormMain : Form
{
    private readonly IMemberService _memberService;
    private readonly ILogger<FormMain> _logger;
    public FormMain(IMemberService memberService, ILogger<FormMain> logger)
    {
        _memberService = memberService;
        _logger = logger;
        InitializeComponent();
    }

    private void FormMain_Load(object sender, EventArgs e)
    {
        
    }
}