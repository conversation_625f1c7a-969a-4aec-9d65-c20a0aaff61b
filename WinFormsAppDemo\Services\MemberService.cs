﻿using WinFormsAppDemo.Interfaces;
using WinFormsAppDemo.Models;

namespace WinFormsAppDemo.Services;

public class MemberService(IFreeSql fSql) : IMemberService
{
    public async Task<int> AddMemberAsync(Member member)
    {
        return await fSql.Insert<Member>().AppendData(member).ExecuteAffrowsAsync();
    }

    public Task<int> UpdateMemberAsync(Member member)
    {
        return fSql.Update<Member>().SetSource(member).ExecuteAffrowsAsync();
    }

    public Task<int> DeleteMemberAsync(string account)
    {
        return fSql.Delete<Member>().Where(m => m.Account == account).ExecuteAffrowsAsync();
    }

    public Task<Member> GetMemberAsync(string account)
    {
        return fSql.Select<Member>().Where(m => m.Account == account).FirstAsync();
    }

    public Task<List<Member>> GetMembersAsync()
    {
        return fSql.Select<Member>().ToListAsync();
    }
}