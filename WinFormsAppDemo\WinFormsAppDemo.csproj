﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>WinExe</OutputType>
        <TargetFramework>net8.0-windows</TargetFramework>
        <Nullable>enable</Nullable>
        <UseWindowsForms>true</UseWindowsForms>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
      <Compile Update="Forms\FormMain.cs">
        <SubType>Form</SubType>
      </Compile>
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="FreeSql" Version="3.5.212" />
      <PackageReference Include="FreeSql.Provider.Sqlite" Version="3.5.212" />
      <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.7" />
      <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.7" />
      <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.7" />
      <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.7" />
      <PackageReference Include="Serilog" Version="4.3.0" />
      <PackageReference Include="Serilog.Extensions.Logging" Version="9.0.2" />
      <PackageReference Include="Serilog.Settings.Configuration" Version="9.0.0" />
      <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
      <PackageReference Include="Serilog.Sinks.File" Version="7.0.0" />
    </ItemGroup>

</Project>